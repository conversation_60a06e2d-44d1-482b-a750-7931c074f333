{"$schema": "https://ui.shadcn.com/schema.json", "style": "new-york", "rsc": true, "tsx": true, "tailwind": {"config": "tailwind.config.ts", "css": "src/app/styles/index.css", "baseColor": "zinc", "cssVariables": true, "prefix": "tw-"}, "aliases": {"components": "@/app/_components/shadcn/ui", "ui": "@/app/_components/shadcn/ui", "utils": "@/app/_components/shadcn/utils", "lib": "@/app/_components/shadcn/libs", "hooks": "@/app/_components/shadcn/hooks"}, "iconLibrary": "lucide"}