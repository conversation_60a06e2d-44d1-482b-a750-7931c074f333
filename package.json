{"name": "next-demo1", "version": "0.1.0", "private": true, "scripts": {"dev": "rimraf -rf .next src/database/db.json && next dev --turbopack", "build": "rimraf -rf .next && next build", "start": "next start", "------------------ lint command": "----", "lint": "pnpm lint:es && pnpm lint:style", "lint:es": "next lint --fix", "lint:style": "stylelint \"**/*.css\" --fix --cache --cache-location node_modules/.cache/stylelint/", "------------------ ui command": "----", "addsc": "pnpm dlx shadcn@latest add"}, "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@ant-design/nextjs-registry": "^1.0.2", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@faker-js/faker": "^9.8.0", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.0", "antd": "^5.24.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "deepmerge": "^4.3.1", "immer": "^10.1.1", "lodash": "^4.17.21", "lucide-react": "^0.488.0", "next": "15.3.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "utility-types": "^3.11.0", "uuid": "^11.1.0", "zustand": "^5.0.4"}, "devDependencies": {"@antfu/eslint-config": "^4.12.0", "@eslint-react/eslint-plugin": "^1.47.4", "@eslint/eslintrc": "^3.3.1", "@next/eslint-plugin-next": "^15.3.1", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.24.0", "eslint-config-next": "15.3.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-perfectionist": "^4.11.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-tailwindcss": "^3.18.0", "eslint-plugin-unused-imports": "^4.1.4", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "postcss-mixins": "^11.0.3", "postcss-nested": "^7.0.2", "postcss-nesting": "^13.0.1", "prettier": "^3.5.3", "rimraf": "^6.0.1", "stylelint": "^16.18.0", "stylelint-config-css-modules": "^4.4.0", "stylelint-config-recess-order": "^6.0.0", "stylelint-config-standard": "^38.0.0", "stylelint-config-tailwindcss": "^1.0.0", "stylelint-prettier": "^5.0.3", "tailwindcss": "^3.3.5", "typescript": "^5"}}