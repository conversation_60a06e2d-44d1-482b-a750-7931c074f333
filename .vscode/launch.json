{"version": "0.2.0", "configurations": [{"name": "Next.js: debug server-side", "type": "node-terminal", "request": "launch", "command": "pnpm dev", "cwd": "${workspaceFolder}", "skipFiles": ["<node_internals>/**"]}, {"name": "Next.js: debug client-side", "type": "chrome", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}", "sourceMapPathOverrides": {"webpack://_n_e/*": "${webRoot}/*"}}, {"name": "Next.js: debug full stack", "type": "node-terminal", "request": "launch", "command": "pnpm dev", "serverReadyAction": {"pattern": "started server on .+, url: (https?://.+)", "uriFormat": "%s", "action": "debugWithChrome"}}]}