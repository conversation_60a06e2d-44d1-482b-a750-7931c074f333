import { getRandomInt } from '@/libs/random';
import { existsSync, readFileSync, writeFileSync } from 'node:fs';
import path, { resolve } from 'node:path';
import { fileURLToPath } from 'node:url';
import { v4 } from 'uuid';

import type { IPost } from './types';

import { faker } from './utils';

// 获取当前文件的目录
const __dirname = path.dirname(fileURLToPath(import.meta.url));

/**
 * 初始数据，生成22篇文章
 */
const posts: IPost[] = [...Array.from({ length: 22 }).keys()].map(() => ({
    id: v4(),
    thumb: `/uploads/thumb/post-${getRandomInt(1, 8)}.png`,
    title: faker.lorem.paragraph({ min: 1, max: 3 }),
    body: faker.lorem.paragraphs(getRandomInt(3, 6), '\n'),
    summary: Math.random() < 0.5 ? faker.lorem.text() : undefined,
}));

/**
 * 检测数据库文件，如果不存在则创建并把初始数据写入
 */
const checkDbFile = async () => {
    const dbPath = resolve(__dirname, 'db.json');
    if (!existsSync(dbPath)) {
        const json = JSON.stringify(posts);
        writeFileSync(dbPath, json);
    }
};

/**
 * 读取数据库文件中的文章数据
 */
export const readDbFile = async (): Promise<IPost[]> => {
    await checkDbFile();
    const dbPath = resolve(__dirname, 'db.json');
    const data = readFileSync(dbPath, 'utf-8');
    return JSON.parse(data);
};

/**
 * 重写数据库文件
 * @param data
 */
export const resetDbFile = async (data: IPost[]) => {
    await checkDbFile();
    const dbPath = resolve(__dirname, 'db.json');
    const json = JSON.stringify(data);
    writeFileSync(dbPath, json);
};
