'use client';
import type { FC, PropsWithChildren } from 'react';

import Theme from '@/app/_components/theme';
import { useAntdAlgorithm } from '@/app/_components/theme/hooks';
import { Locale } from '@/app/demo/_components/context';
import { localeData } from '@/app/demo/_components/context/constants';
// import zhCN from 'antd/locale/zh_CN';
import '@ant-design/v5-patch-for-react-19';
import { useLocale } from '@/app/demo/_components/hooks';
import { LayoutStore } from '@/app/demo/_components/zustand';
import { px2remTransformer, StyleProvider } from '@ant-design/cssinjs';
import { AntdRegistry } from '@ant-design/nextjs-registry';
import { App as AntdApp, ConfigProvider } from 'antd';
import React, { useMemo } from 'react';

import $styles from './layout.module.css';

const px2rem = px2remTransformer();
const DemoAntd: FC<PropsWithChildren> = ({ children }) => {
    const algorithm = useAntdAlgorithm();
    const locale = useLocale();
    const antdLocaleData = useMemo(() => {
        if (!Object.keys(localeData).find((v) => v === locale.name)) {
            return localeData[0];
        }
        return localeData[locale.name];
    }, [locale.name]);
    return (
        <ConfigProvider
            locale={antdLocaleData}
            theme={{
                algorithm,
                // 启用css变量
                cssVar: true,
                hashed: false,
                token: {},
            }}
        >
            <AntdApp>
                <StyleProvider transformers={[px2rem]}>
                    <div className={$styles.layout}>{children}</div>
                </StyleProvider>
            </AntdApp>
        </ConfigProvider>
    );
};
const DemoLayout: FC<PropsWithChildren> = ({ children }) => (
    // <AntdRegistry>
    //     <ConfigProvider
    //         locale={zhCN}
    //         theme={{
    //             algorithm: theme.defaultAlgorithm,
    //             // 启用css变量
    //             cssVar: true,
    //             hashed: false,
    //             token: {},
    //         }}
    //     >
    //         <AntdApp>
    //             <StyleProvider transformers={[px2rem]}>
    //                 <div className={$styles.layout}>{children}</div>
    //             </StyleProvider>
    //         </AntdApp>
    //     </ConfigProvider>
    // </AntdRegistry>
    <AntdRegistry>
        <LayoutStore>
            <Locale>
                <Theme>
                    <DemoAntd>
                        <div className={$styles.layout}>{children}</div>
                    </DemoAntd>
                </Theme>
            </Locale>
        </LayoutStore>
    </AntdRegistry>
);
export default DemoLayout;
