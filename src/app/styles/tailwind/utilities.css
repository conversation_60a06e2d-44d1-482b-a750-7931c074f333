@layer utilities {
    /* 只显示一行文字, 溢出部分以省略号代替 */
    .tw-ellips {
        @apply tw-inline-block tw-overflow-hidden tw-max-w-full tw-whitespace-nowrap tw-text-ellipsis tw-break-all;
    }

    /* 鼠标移动到文字或链接出现动画下划线 */
    .tw-animate-decoration {
        padding-bottom: 2px;
        background: linear-gradient(hsl(var(--foreground)), hsl(var(--foreground))) 0% 100% / 0% 1px
            no-repeat;
        transition: background-size ease-out 200ms;

        &:not(:focus):hover {
            background-size: 100% 1px;
        }
    }

    /* 粗下划线 */
    .tw-animate-decoration-lg:not(:focus):hover {
        background-size: 100% 2px;
    }

    /* 取消下划线 */
    .tw-none-animate-decoration {
        background-size: 0 !important;
        transition: none !important;

        &:hover,
        &:not(:focus):hover {
            background-size: 100% 0 !important;
        }
    }
}
