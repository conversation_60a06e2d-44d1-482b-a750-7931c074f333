import type { FC } from 'react';

import CallbackDemo from './_components/callback';
import ContextDemo from './_components/context';
import EffectDemo from './_components/effect';
import MemoDemo from './_components/memo';
import ReducerDemo from './_components/reducer';
import RefDemo from './_components/ref';
import StateDemo from './_components/state';

const DemoPage: FC = () => (
    <>
        <StateDemo />
        <EffectDemo />
        <RefDemo />
        <MemoDemo />
        <CallbackDemo />
        <ContextDemo />
        <ReducerDemo />
    </>
);

export default DemoPage;
