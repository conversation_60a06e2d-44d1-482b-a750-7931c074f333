import { createContext } from 'react';

import type { LayoutStateType } from '../_components/zustand/types';

export enum LayoutMode {
    TOP = 'top',
    SIDE = 'side',
    CONTENT = 'content',
}

export enum LayoutComponent {
    HEADER = 'header',
    SIDEBAR = 'sidebar',
}

export enum LayoutActionType {
    CHANGE_MODE = 'change_mode',
    CHANGE_THEME = 'change_theme',
}

export const LayoutContext = createContext<LayoutStateType | null>(null);
