'use client';
import type { FC, PropsWithChildren } from 'react';

import { px2remTransformer, StyleProvider } from '@ant-design/cssinjs';
import { AntdRegistry } from '@ant-design/nextjs-registry';
import { App as AntdApp, ConfigProvider, theme } from 'antd';
import React, { useMemo } from 'react';
// import zhCN from 'antd/locale/zh_CN';
import '@ant-design/v5-patch-for-react-19';

import { Locale } from './_components/context';
import { localeData } from './_components/context/constants';
import { useLocale, useTheme } from './_components/hooks';
import { Theme } from './_components/reducer';
import { LayoutStore } from './_components/zustand';
import $styles from './layout.module.css';

const px2rem = px2remTransformer();
const DemoAntd: FC<PropsWithChildren> = ({ children }) => {
    const themeState = useTheme();
    const algorithm = useMemo(() => {
        const result = [themeState.compact ? theme.compactAlgorithm : theme.defaultAlgorithm];
        if (themeState.mode === 'dark') {
            result.push(theme.darkAlgorithm);
        }
        return result;
    }, [themeState]);
    const locale = useLocale();
    const antdLocaleData = useMemo(() => {
        if (!Object.keys(localeData).find((v) => v === locale.name)) {
            return localeData[0];
        }
        return localeData[locale.name];
    }, [locale.name]);
    return (
        <ConfigProvider
            locale={antdLocaleData}
            theme={{
                algorithm,
                // 启用css变量
                cssVar: true,
                hashed: false,
                token: {},
            }}
        >
            <AntdApp>
                <StyleProvider transformers={[px2rem]}>
                    <div className={$styles.layout}>{children}</div>
                </StyleProvider>
            </AntdApp>
        </ConfigProvider>
    );
};
const DemoLayout: FC<PropsWithChildren> = ({ children }) => (
    // <AntdRegistry>
    //     <ConfigProvider
    //         locale={zhCN}
    //         theme={{
    //             algorithm: theme.defaultAlgorithm,
    //             // 启用css变量
    //             cssVar: true,
    //             hashed: false,
    //             token: {},
    //         }}
    //     >
    //         <AntdApp>
    //             <StyleProvider transformers={[px2rem]}>
    //                 <div className={$styles.layout}>{children}</div>
    //             </StyleProvider>
    //         </AntdApp>
    //     </ConfigProvider>
    // </AntdRegistry>
    <AntdRegistry>
        <LayoutStore>
            <Locale>
                <Theme>
                    <DemoAntd>
                        <div className={$styles.layout}>{children}</div>
                    </DemoAntd>
                </Theme>
            </Locale>
        </LayoutStore>
    </AntdRegistry>
);
export default DemoLayout;
