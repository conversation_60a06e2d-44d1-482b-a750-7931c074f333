import type { Dispatch } from 'react';

export enum ThemeMode {
    Light = 'light',
    Dark = 'dark',
}

export interface ThemeState {
    mode: `${ThemeMode}`;
    compact: boolean;
}

export enum ThemeActionType {
    CHANGE_MODE = 'change_mode',
    CHANGE_COMPACT = 'change_compact',
}

export type ThemeAction =
    | { type: `${ThemeActionType.CHANGE_MODE}`; value: `${ThemeMode}` }
    | { type: `${ThemeActionType.CHANGE_COMPACT}`; value: boolean };

export interface ThemeContextType {
    state: ThemeState;
    dispatch: Dispatch<ThemeAction>;
}
