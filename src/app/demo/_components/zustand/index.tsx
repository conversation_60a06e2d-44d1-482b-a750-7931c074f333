'use client';
import type { FC, PropsWithChildren } from 'react';
import type { DeepPartial } from 'utility-types';

import { LayoutContext, LayoutMode } from '@/app/demo/zustand/constants';
import { Layout as AntdLayout, Menu as AntdMenu, Select, Switch, theme } from 'antd';
import {
    Content as Antd<PERSON>ontent,
    <PERSON><PERSON> as <PERSON>t<PERSON><PERSON><PERSON><PERSON>,
    <PERSON>er as Antd<PERSON>eader,
} from 'antd/es/layout/layout';
import { default as AntdSider } from 'antd/es/layout/Sider';
import clsx from 'clsx';
import { isNil } from 'lodash';
import { useCallback, useRef } from 'react';
import { useShallow } from 'zustand/shallow';

import type { LayoutOptions, LayoutStateType } from './types';

import { useLayoutContext } from '../hooks';
import $styles from '../style.module.css';
import { createLayoutStore } from './store';

const items = Array.from({ length: 15 }, (_, index) => ({ key: index, label: `nav ${index + 1}` }));

const Menu: FC = () => {
    const mode = useLayoutContext((state) => state.mode);
    const layoutTheme = useLayoutContext((state) => state.theme);
    return (
        <AntdMenu
            theme={mode === 'top' ? layoutTheme.header : layoutTheme.sidebar}
            mode={mode === 'top' ? 'horizontal' : 'inline'}
            defaultSelectedKeys={['2']}
            items={items}
            style={{ flex: 1, minWidth: 0 }}
        />
    );
};

const Sider: FC = () => {
    const {
        mode,
        theme: { sidebar },
    } = useLayoutContext(useShallow((state) => ({ mode: state.mode, theme: state.theme })));
    const {
        token: { colorBgContainer },
    } = theme.useToken();
    return (
        <AntdSider style={{ background: sidebar === 'dark' ? '' : colorBgContainer }}>
            {mode === 'side' && (
                <div className="tw-mx-auto tw-my-7 tw-h-10 tw-w-3/4 tw-bg-slate-500" />
            )}
            <Menu />
        </AntdSider>
    );
};

const Header: FC = () => {
    const mode = useLayoutContext((state) => state.mode);
    const layoutTheme = useLayoutContext((state) => state.theme);
    const {
        token: { colorBgContainer },
    } = theme.useToken();
    return (
        <AntdHeader
            style={{ background: layoutTheme.header === 'dark' ? '' : colorBgContainer }}
            className="tw-flex tw-items-center tw-px-0"
        >
            {mode !== 'side' && <div className="tw-mx-3 tw-h-10 tw-w-44 tw-bg-slate-500" />}
            {mode === 'top' && <Menu />}
        </AntdHeader>
    );
};

const Content: FC = () => {
    const {
        token: { colorBgContainer, borderRadiusLG },
    } = theme.useToken();
    return (
        <AntdLayout className="tw-p-6">
            <AntdContent
                style={{
                    padding: 24,
                    margin: 0,
                    minHeight: 280,
                    background: colorBgContainer,
                    borderRadius: borderRadiusLG,
                }}
            >
                Content
            </AntdContent>
        </AntdLayout>
    );
};

const Footer: FC = () => (
    <AntdFooter style={{ textAlign: 'center' }}>
        Ant Design ©{new Date().getFullYear()} Created by Ant UED
    </AntdFooter>
);

const ModeCtrl: FC = () => {
    const mode = useLayoutContext((state) => state.mode);
    const changeMode = useLayoutContext((state) => state.changeMode);
    return (
        <Select defaultValue={mode} onChange={changeMode} style={{ width: 300 }}>
            <Select.Option value={LayoutMode.TOP}>顶栏菜单</Select.Option>
            <Select.Option value={LayoutMode.SIDE}>左栏菜单【LOGO在边栏】</Select.Option>
            <Select.Option value={LayoutMode.CONTENT}>左栏菜单【LOGO在顶栏】</Select.Option>
        </Select>
    );
};

const ThemeCtrl: FC = () => {
    const theme = useLayoutContext((state) => state.theme);
    const changeTheme = useLayoutContext((state) => state.changeTheme);
    const changeHeaderTheme = useCallback(
        (value: boolean) => changeTheme({ header: value ? 'dark' : 'light' }),
        [],
    );
    const changeSidebarTheme = useCallback(
        (value: boolean) => changeTheme({ sidebar: value ? 'dark' : 'light' }),
        [],
    );
    return (
        <>
            <div>
                <span>切换侧边栏主题：</span>
                <Switch
                    checkedChildren="🌛"
                    unCheckedChildren="☀️"
                    onChange={changeSidebarTheme}
                    checked={theme.sidebar === 'dark'}
                    defaultChecked={theme.sidebar === 'dark'}
                />
            </div>
            <div>
                <span>切换顶栏主题：</span>
                <Switch
                    checkedChildren="🌛"
                    unCheckedChildren="☀️"
                    onChange={changeHeaderTheme}
                    checked={theme.header === 'dark'}
                    defaultChecked={theme.header === 'dark'}
                />
            </div>
        </>
    );
};

export const ZustandDemo = () => {
    const mode = useLayoutContext((state) => state.mode);
    return (
        <div className="tw-flex tw-flex-auto tw-flex-col tw-items-center tw-justify-center">
            <div className={clsx($styles.container, 'tw-flex tw-w-[100rem] tw-justify-between')}>
                <ModeCtrl />
                <ThemeCtrl />
            </div>
            <div className={clsx($styles.container, 'tw-w-[100rem]')}>
                <AntdLayout>
                    {mode !== 'side' && <Header />}
                    {mode === 'side' && <Sider />}
                    <AntdLayout>
                        {mode === 'side' && <Header />}
                        {mode === 'content' && <Sider />}
                        {mode !== 'content' && <Content />}
                        {mode !== 'content' && <Footer />}
                        {mode === 'content' && (
                            <AntdLayout>
                                <Content />
                                <Footer />
                            </AntdLayout>
                        )}
                    </AntdLayout>
                </AntdLayout>
            </div>
        </div>
    );
};

export const LayoutStore: FC<PropsWithChildren<DeepPartial<LayoutOptions>>> = ({
    children,
    ...props
}) => {
    const storeRef = useRef<LayoutStateType>(null);
    if (isNil(storeRef.current)) {
        storeRef.current = createLayoutStore(props);
    }
    return <LayoutContext value={storeRef.current}>{children}</LayoutContext>;
};
