import type { DeepPartial } from 'utility-types';

import { deepMerge } from '@/libs/utils';
import { isNil } from 'lodash';
import { createStore } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
// import { shallow } from 'zustand/shallow';

import type { LayoutOptions, LayoutState } from './types';

export const createLayoutStore = (options: DeepPartial<LayoutOptions> = {}) =>
    createStore<LayoutState>()(
        subscribeWithSelector(
            devtools(
                persist(
                    immer((set) => ({
                        ...deepMerge<LayoutOptions, DeepPartial<LayoutOptions>>(
                            {
                                mode: 'side',
                                theme: {
                                    header: 'light',
                                    sidebar: 'dark',
                                },
                            },
                            options,
                            'replace',
                        ),
                        changeMode: (value) => set(() => ({ mode: value })),
                        changeTheme: (value) =>
                            set((state) => {
                                if (!isNil(value.sidebar)) state.theme.sidebar = value.sidebar;
                                else if (!isNil(value.header))
                                    state.theme.sidebar =
                                        value.header === 'light' ? 'dark' : 'light';
                                state.theme.header =
                                    state.theme.sidebar === 'light' ? 'dark' : 'light';
                            }),
                    })),
                    {
                        name: 'zustand-demo',
                    },
                ),
                {
                    name: 'zustand-demo',
                },
            ),
        ),
    );

// export const useLayoutStore = createLayoutStore();
// useLayoutStore.subscribe(
//     (state) => state.mode,
//     (value) => {
//         console.log('value changed', value);
//     },
//     {
//         equalityFn: shallow,
//         fireImmediately: true,
//     },
// );
