import type { LayoutComponent, LayoutMode } from '@/app/demo/zustand/constants';

import type { createLayoutStore } from './store';

export enum ThemeMode {
    LIGHT = 'light',
    DARK = 'dark',
}

export interface LayoutOptions {
    mode: `${LayoutMode}`;
    theme: Partial<LayoutTheme>;
}

export interface LayoutActions {
    changeMode: (value: `${LayoutMode}`) => void;
    changeTheme: (value: Partial<LayoutTheme>) => void;
}

export type LayoutState = LayoutOptions & LayoutActions;
export type LayoutTheme = { [key in `${LayoutComponent}`]: `${ThemeMode}` };
export type LayoutStateType = ReturnType<typeof createLayoutStore>;
