'use client';
import type { FC } from 'react';

import { Button } from 'antd';
import clsx from 'clsx';
import { memo, useCallback, useEffect, useState } from 'react';

import $styles from './style.module.css';

const Info: FC<{ call: () => void }> = memo(() => {
    console.log('渲染组件');
    return null;
});
const CallbackDemo: FC = () => {
    const [, setCount] = useState<number>(0);
    const changeCount = () => setCount(Math.ceil(Math.random() * 10));
    const getInfo = useCallback(() => {}, []);
    useEffect(() => {
        console.log('getInfo改变');
    }, [getInfo]);
    return (
        <div className={clsx($styles.container, 'tw-w-80')}>
            <h2 className="tw-text-center">useCallback Demo</h2>
            <div className="tw-flex tw-justify-around">
                <Button onClick={changeCount} type="dashed">
                    变化
                </Button>
            </div>
            <Info call={getInfo} />
        </div>
    );
};
export default CallbackDemo;
