'use client';
import type { FC, PropsWithChildren } from 'react';

import { Pagination, Select } from 'antd';
import React, { use, useCallback, useMemo, useState } from 'react';

import type { LocaleState, LocaleType } from './types';

import $styles from '../style.module.css';
import { localeData, locales } from './constants';
import { LocaleContext } from './context';

const LocaleProvider: FC<PropsWithChildren<LocaleState>> = ({ locale, setLocale, children }) => {
    const value = useMemo(() => ({ locale, setLocale }), [locale]);
    return <LocaleContext value={value}>{children}</LocaleContext>;
};

export const Locale: FC<PropsWithChildren> = ({ children }) => {
    const [locale, setLocale] = useState<LocaleType>(locales[0]);
    const changeLocale = useCallback((value: LocaleType) => {
        if (Object.keys(localeData).find((v) => v === value.name)) {
            setLocale(value);
        }
    }, []);
    return (
        <LocaleProvider locale={locale} setLocale={changeLocale}>
            {children}
        </LocaleProvider>
    );
};

export const LocaleConfig: FC = () => {
    const { locale, setLocale } = use(LocaleContext);
    const changeLocale = (value: string) => {
        const current = locales.find((v) => v.name === value);
        if (current) {
            setLocale(current);
        }
    };
    return (
        <Select defaultValue={locale.name} onChange={changeLocale} style={{ width: 120 }}>
            {locales.map(({ name, label }) => (
                <Select.Option key={name} value={name}>
                    {label}
                </Select.Option>
            ))}
        </Select>
    );
};

const ContextDemo: FC = () => {
    const { locale } = use(LocaleContext);
    return (
        <div className={$styles.container}>
            <h2 className="tw-text-center">useContext Demo</h2>
            <p className="tw-py-5 tw-text-center">当前语言：{locale.label}</p>
            <div className="tw-w-full">
                <h3>Antd语言切换测试</h3>
                <div className="tw-my-4 tw-w-full">
                    <LocaleConfig />
                </div>
                <Pagination defaultCurrent={0} total={500} showSizeChanger showQuickJumper />
            </div>
        </div>
    );
};
export default ContextDemo;
