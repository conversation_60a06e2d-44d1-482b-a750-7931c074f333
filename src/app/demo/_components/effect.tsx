'use client';
import type { FC } from 'react';

import { Button } from 'antd';
import { useEffect, useState } from 'react';

import $styles from './style.module.css';

const EffectDemo: FC = () => {
    const [ghost, setGhost] = useState<boolean>(false);
    const [width, setWidth] = useState<number>(0);
    const [red, setRed] = useState<boolean>(false);
    const toggleGhostBtn = () => setGhost(!ghost);
    const resizeWidth = () => setWidth(window.innerWidth);
    useEffect(() => {
        console.log('窗口宽度改变！');
    }, [width]);
    useEffect(() => {
        console.log('切换幽灵按钮！');
        (async () => {
            await new Promise((resolve) => {
                setTimeout(() => resolve(true), 3000);
            });
            setRed(ghost);
        })();
    }, [ghost]);
    useEffect(() => {
        resizeWidth();
        window.addEventListener('resize', resizeWidth);
        console.log('组件加载完毕！');
        return () => {
            window.removeEventListener('resize', resizeWidth);
        };
    }, []);
    return (
        <div className={$styles.container}>
            <h2 className="tw-text-center">useEffect Demo</h2>
            <p className="tw-py-5 tw-text-center">{ghost ? 'ghost' : '普通'}按钮</p>
            <div className="tw-flex tw-flex-col tw-justify-center">
                <Button type="primary" ghost={ghost} onClick={toggleGhostBtn} danger={red}>
                    切换按钮
                </Button>
                <p className="tw-py-5 tw-text-center">{width}</p>
            </div>
        </div>
    );
};
export default EffectDemo;
