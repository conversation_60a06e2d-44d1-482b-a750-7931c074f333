'use client';
import { isNil } from 'lodash';
import { use, useCallback, useMemo } from 'react';
import { useStore } from 'zustand';
import { useShallow } from 'zustand/shallow';

import type { LocaleType } from './context/types';
import type { ThemeAction } from './reducer/types';
import type { LayoutState } from './zustand/types';

import { LayoutContext } from '../zustand/constants';
import { locales } from './context/constants';
import { LocaleContext } from './context/context';
import { defaultThemeConfig, ThemeContext } from './reducer/constants';

export const useTheme = () => {
    const context = use(ThemeContext) ?? ({} as Record<string, any>);
    return useMemo(
        () => (isNil(context.state) ? defaultThemeConfig : context.state),
        [context.state],
    );
};
export const useThemeAction = () => {
    const context = use(ThemeContext) ?? ({} as Record<string, any>);
    return useCallback(isNil(context.dispatch) ? (_params: ThemeAction) => {} : context.dispatch, [
        context.dispatch,
    ]);
};

export const useLocale = () => {
    const context = use(LocaleContext) ?? ({} as Record<string, any>);
    return useMemo(() => (isNil(context.locale) ? locales[0] : context.locale), [context.locale]);
};
export const useLocaleAction = () => {
    const context = use(LocaleContext) ?? ({} as Record<string, any>);
    return useCallback(isNil(context.setLocale) ? (_locale: LocaleType) => {} : context.setLocale, [
        context.setLocale,
    ]);
};

export function useLayoutContext<T>(selector: (state: LayoutState) => T): T {
    const store = use(LayoutContext);
    if (!store) throw new Error('Missing LayoutContext.Provider in the tree');
    return useStore(store, useShallow(selector));
}
