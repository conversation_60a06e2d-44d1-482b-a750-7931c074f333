'use client';
import type { FC } from 'react';

import { Switch } from 'antd';
import { Moon, Sun } from 'lucide-react';
import { useEffect } from 'react';

import { Button } from '../shadcn/ui/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '../shadcn/ui/dropdown-menu';
import { useTheme, useThemeActions } from './hooks';
export const AntdThemeSetting: FC = () => {
    const { mode, compact } = useTheme();
    const { changeMode, toggleMode, toggleCompact } = useThemeActions();
    useEffect(() => {
        changeMode(mode);
    }, []);
    return (
        <>
            <Switch
                checkedChildren="🌛"
                unCheckedChildren="☀️"
                onChange={toggleMode}
                checked={mode === 'dark'}
                defaultChecked={mode === 'dark'}
            />
            <Switch
                checkedChildren="紧凑"
                unCheckedChildren="正常"
                onChange={toggleCompact}
                checked={compact}
                defaultChecked={compact}
            />
        </>
    );
};

export const ShadcnThemeSetting: FC = () => {
    const { changeMode } = useThemeActions();
    return (
        <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
                <Button variant="outline" size="icon" className="focus-visible:!tw-ring-0">
                    <Sun className="tw-h-[1.2rem] tw-w-[1.2rem] tw-rotate-0 tw-scale-100 tw-transition-all dark:tw--rotate-90 dark:tw-scale-0" />
                    <Moon className="tw-absolute tw-h-[1.2rem] tw-w-[1.2rem] tw-rotate-90 tw-scale-0 tw-transition-all dark:tw-rotate-0 dark:tw-scale-100" />
                    <span className="tw-sr-only">Toggle theme</span>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => changeMode('light')}>Light</DropdownMenuItem>
                <DropdownMenuItem onClick={() => changeMode('dark')}>Dark</DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    );
};
