import type { Reducer } from 'react';

import { createPersistReduxStore } from '@/libs/store';
import { produce } from 'immer';

import type { ThemeDispatchs, ThemeOptions } from './types';

import { defaultOptions, ThemeActions } from './constants';
import { getSystemTheme } from './hooks';

const ThemeReducer: Reducer<ThemeOptions, ThemeDispatchs> = produce((draft, action) => {
    switch (action.type) {
        case ThemeActions.CHANGE_MODE:
            if (action.value !== 'system') {
                draft.mode = action.value;
            } else if (draft.mode === 'system') {
                draft.mode = getSystemTheme();
            }
            break;
        case ThemeActions.TOGGLE_MODE:
            if (draft.mode === 'system') {
                draft.mode = getSystemTheme() === 'dark' ? 'light' : 'dark';
            } else {
                draft.mode = draft.mode === 'dark' ? 'light' : 'dark';
            }
            break;
        case ThemeActions.CHANGE_COMPACT:
            draft.compact = action.value;
            break;
        case ThemeActions.TOGGLE_COMPACT:
            draft.compact = !draft.compact;
            break;
        default:
            break;
    }
});

export const createThemeStore = (options: Partial<ThemeOptions> = {}) =>
    createPersistReduxStore(
        ThemeReducer,
        {
            ...defaultOptions,
            ...options,
        },
        {
            name: 'theme',
            partialize: (state) => ({
                mode: state.mode,
                compact: state.compact,
            }),
        },
    );
