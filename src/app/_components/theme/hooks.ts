'use client';
import { theme } from 'antd';
import { debounce } from 'lodash';
import { use, useCallback, useEffect, useMemo, useState } from 'react';
import { useStore } from 'zustand';
import { useShallow } from 'zustand/shallow';

import type { ThemeMode } from './constants';
import type { ThemeState } from './types';

import { ThemeContext } from './constants';

export function useThemeStore() {
    const store = use(ThemeContext);
    if (!store) throw new Error('Missing ThemeContext.Provider in the tree');
    return store;
}

export function useThemeState<T>(selector: (state: ThemeState) => T): T {
    const store = useThemeStore();
    return useStore(store, useShallow(selector));
}

export const getSystemTheme = () => {
    if (typeof window === 'undefined') return 'light';
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
};

export const useSystemTheme = (): 'light' | 'dark' => {
    const [theme, setTheme] = useState<'light' | 'dark'>(() => getSystemTheme());
    useEffect(() => {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        const handler = () => setTheme(mediaQuery.matches ? 'dark' : 'light');
        mediaQuery.addEventListener('change', handler);
        return () => mediaQuery.removeEventListener('change', handler);
    }, []);
    return theme;
};

export const useTheme = () =>
    useThemeState((state) => ({ mode: state.mode, compact: state.compact }));

export const useThemeColor = () => {
    const { mode } = useTheme();
    const systemTheme = useSystemTheme();
    return mode === 'system' ? systemTheme : mode;
};

export const useThemeActions = () => {
    const dispatch = useThemeState((state) => state.dispatch);
    return {
        changeMode: useCallback(
            debounce((v: `${ThemeMode}`) => dispatch({ type: 'change_mode', value: v }), 100, {}),
            [],
        ),
        toggleMode: useCallback(
            debounce(() => dispatch({ type: 'toggle_mode' }), 100, {}),
            [],
        ),
        changeCompact: useCallback(
            debounce((v: boolean) => dispatch({ type: 'change_compact', value: v }), 100, {}),
            [],
        ),
        toggleCompact: useCallback(
            debounce(() => dispatch({ type: 'toggle_compact' }), 100, {}),
            [],
        ),
    };
};

export const useAntdAlgorithm = () => {
    const { mode, compact } = useTheme();
    const systemTheme = useSystemTheme();
    return useMemo(() => {
        const result = [compact ? theme.compactAlgorithm : theme.defaultAlgorithm];
        if (mode === 'dark' || (mode === 'system' && systemTheme === 'dark')) {
            result.push(theme.darkAlgorithm);
        }
        return result;
    }, [mode, compact, systemTheme]);
};
