import { createContext } from 'react';

import type { ThemeOptions, ThemeStoreType } from './types';

export enum ThemeMode {
    LIGHT = 'light',
    DARK = 'dark',
    SYSTEM = 'system',
}

export enum ThemeActions {
    CHANGE_MODE = 'change_mode',
    TOGGLE_MODE = 'toggle_mode',
    CHANGE_COMPACT = 'change_compact',
    TOGGLE_COMPACT = 'toggle_compact',
}

export const defaultOptions: ThemeOptions = {
    mode: ThemeMode.SYSTEM,
    compact: false,
};

export const ThemeContext = createContext<ThemeStoreType | null>(null);
