import type { ThemeActions, ThemeMode } from './constants';
import type { createThemeStore } from './store';

export interface ThemeOptions {
    mode: `${ThemeMode}`;
    compact: boolean;
}

export type ThemeDispatchs =
    | { type: `${ThemeActions.CHANGE_MODE}`; value: `${ThemeMode}` }
    | { type: `${ThemeActions.TOGGLE_MODE}` }
    | { type: `${ThemeActions.CHANGE_COMPACT}`; value: boolean }
    | { type: `${ThemeActions.TOGGLE_COMPACT}` };

export type ThemeState = ThemeOptions & {
    dispatch: (action: ThemeDispatchs) => ThemeDispatchs;
};

export type ThemeStoreType = ReturnType<typeof createThemeStore>;
