'use client';
import type { FC, PropsWithChildren } from 'react';

import { isNil } from 'lodash';
import { useEffect, useRef } from 'react';

import type { ThemeOptions, ThemeStoreType } from './types';

import { ThemeContext } from './constants';
import { useSystemTheme, useThemeStore } from './hooks';
import { createThemeStore } from './store';

const Theme: FC<PropsWithChildren<Partial<ThemeOptions>>> = ({ children, ...props }) => {
    const storeRef = useRef<ThemeStoreType>(null);
    if (!storeRef.current) {
        storeRef.current = createThemeStore(props);
    }
    return (
        <ThemeContext value={storeRef.current}>
            <ThemeSubscriber>{children}</ThemeSubscriber>
        </ThemeContext>
    );
};
export default Theme;

const ThemeSubscriber: FC<PropsWithChildren> = ({ children }) => {
    const systemTheme = useSystemTheme();
    const store = useThemeStore();
    let unSub: () => void;
    useEffect(() => {
        unSub = store.subscribe(
            (state) => state.mode,
            (m) => {
                const html = document.getElementsByTagName('html');
                if (html.length) {
                    html[0].classList.remove('light');
                    html[0].classList.remove('dark');
                    html[0].classList.add(m === 'system' ? systemTheme : m);
                }
            },
            {
                fireImmediately: true,
            },
        );
        return () => {
            if (!isNil(unSub)) unSub();
        };
    }, [systemTheme]);
    return <>{children}</>;
};
